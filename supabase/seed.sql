-- Sample data for testing the dashboard
-- This script creates sample buckets and assets with realistic data

-- First, create a sample user (this would normally be handled by auth)
-- For testing purposes, we'll use a fixed UUID .
DO $$
DECLARE
    sample_user_id uuid := '1581f850-4dd5-41ac-a50e-0fe418986224';
    bucket_id_1 uuid;
    bucket_id_2 uuid;
    asset_id_1 uuid;
    asset_id_2 uuid;
    asset_id_3 uuid;
    asset_id_4 uuid;
    asset_id_5 uuid;
    asset_id_6 uuid;
    asset_id_7 uuid;
BEGIN
    -- Create sample buckets
    bucket_id_1 := gen_random_uuid();
    bucket_id_2 := gen_random_uuid();
    
    INSERT INTO buckets (id, name, user_id) VALUES 
        (bucket_id_1, 'Tech Stocks', sample_user_id),
        (bucket_id_2, 'Crypto Portfolio', sample_user_id);
    
    -- Create sample assets with historical data (snapshots)
    -- Apple Inc - with price changes over time (showing growth trend)
    asset_id_1 := gen_random_uuid();
    INSERT INTO assets (asset_identifier, bucket_id, name, symbol, type, quantity, unit_price, currency_symbol, snapshot_type, holding_date, country, created_at, user_id) VALUES
        (asset_id_1, bucket_id_1, 'Apple Inc', 'AAPL', 'Stock', 100, 150.00, '$', 'create', '2023-10-15', 'USA', '2023-10-15 10:00:00', sample_user_id),
        (asset_id_1, bucket_id_1, 'Apple Inc', 'AAPL', 'Stock', 100, 155.50, '$', 'update', '2023-11-15', 'USA', '2023-11-15 10:00:00', sample_user_id),
        (asset_id_1, bucket_id_1, 'Apple Inc', 'AAPL', 'Stock', 100, 165.25, '$', 'update', '2023-12-15', 'USA', '2023-12-15 10:00:00', sample_user_id),
        (asset_id_1, bucket_id_1, 'Apple Inc', 'AAPL', 'Stock', 100, 170.00, '$', 'update', '2024-01-15', 'USA', '2024-01-15 10:00:00', sample_user_id),
        (asset_id_1, bucket_id_1, 'Apple Inc', 'AAPL', 'Stock', 100, 175.25, '$', 'update', '2024-02-15', 'USA', '2024-02-15 10:00:00', sample_user_id),
        (asset_id_1, bucket_id_1, 'Apple Inc', 'AAPL', 'Stock', 100, 182.50, '$', 'update', '2024-03-15', 'USA', '2024-03-15 10:00:00', sample_user_id);
    
    -- Microsoft Corp
    asset_id_2 := gen_random_uuid();
    INSERT INTO assets (asset_identifier, bucket_id, name, symbol, type, quantity, unit_price, currency_symbol, snapshot_type, holding_date, country, created_at, user_id) VALUES
        (asset_id_2, bucket_id_1, 'Microsoft Corp', 'MSFT', 'Stock', 75, 320.00, '$', 'create', '2023-11-20', 'USA', '2023-11-20 10:00:00', sample_user_id),
        (asset_id_2, bucket_id_1, 'Microsoft Corp', 'MSFT', 'Stock', 75, 335.00, '$', 'update', '2023-12-20', 'USA', '2023-12-20 10:00:00', sample_user_id),
        (asset_id_2, bucket_id_1, 'Microsoft Corp', 'MSFT', 'Stock', 75, 351.00, '$', 'update', '2024-01-20', 'USA', '2024-01-20 10:00:00', sample_user_id),
        (asset_id_2, bucket_id_1, 'Microsoft Corp', 'MSFT', 'Stock', 75, 365.00, '$', 'update', '2024-02-20', 'USA', '2024-02-20 10:00:00', sample_user_id);

    -- Tesla Inc
    asset_id_3 := gen_random_uuid();
    INSERT INTO assets (asset_identifier, bucket_id, name, symbol, type, quantity, unit_price, currency_symbol, snapshot_type, holding_date, country, created_at, user_id) VALUES
        (asset_id_3, bucket_id_1, 'Tesla Inc', 'TSLA', 'Stock', 50, 180.00, '$', 'create', '2023-12-01', 'USA', '2023-12-01 10:00:00', sample_user_id),
        (asset_id_3, bucket_id_1, 'Tesla Inc', 'TSLA', 'Stock', 50, 185.50, '$', 'update', '2024-01-01', 'USA', '2024-01-01 10:00:00', sample_user_id),
        (asset_id_3, bucket_id_1, 'Tesla Inc', 'TSLA', 'Stock', 50, 195.50, '$', 'update', '2024-02-01', 'USA', '2024-02-01 10:00:00', sample_user_id),
        (asset_id_3, bucket_id_1, 'Tesla Inc', 'TSLA', 'Stock', 50, 205.75, '$', 'update', '2024-03-01', 'USA', '2024-03-01 10:00:00', sample_user_id);

    -- Bitcoin
    asset_id_4 := gen_random_uuid();
    INSERT INTO assets (asset_identifier, bucket_id, name, symbol, type, quantity, unit_price, currency_symbol, snapshot_type, holding_date, country, created_at, user_id) VALUES
        (asset_id_4, bucket_id_2, 'Bitcoin', 'BTC', 'Cryptocurrency', 2.5, 35000.00, '$', 'create', '2023-09-15', 'Global', '2023-09-15 10:00:00', sample_user_id),
        (asset_id_4, bucket_id_2, 'Bitcoin', 'BTC', 'Cryptocurrency', 2.5, 42000.00, '$', 'update', '2023-10-15', 'Global', '2023-10-15 10:00:00', sample_user_id),
        (asset_id_4, bucket_id_2, 'Bitcoin', 'BTC', 'Cryptocurrency', 2.5, 45000.00, '$', 'update', '2023-11-15', 'Global', '2023-11-15 10:00:00', sample_user_id),
        (asset_id_4, bucket_id_2, 'Bitcoin', 'BTC', 'Cryptocurrency', 2.5, 48500.00, '$', 'update', '2023-12-15', 'Global', '2023-12-15 10:00:00', sample_user_id),
        (asset_id_4, bucket_id_2, 'Bitcoin', 'BTC', 'Cryptocurrency', 2.5, 52000.00, '$', 'update', '2024-01-15', 'Global', '2024-01-15 10:00:00', sample_user_id),
        (asset_id_4, bucket_id_2, 'Bitcoin', 'BTC', 'Cryptocurrency', 2.5, 55500.00, '$', 'update', '2024-02-15', 'Global', '2024-02-15 10:00:00', sample_user_id);
    
    -- Ethereum
    asset_id_5 := gen_random_uuid();
    INSERT INTO assets (asset_identifier, bucket_id, name, symbol, type, quantity, unit_price, currency_symbol, snapshot_type, holding_date, country, created_at, user_id) VALUES
        (asset_id_5, bucket_id_2, 'Ethereum', 'ETH', 'Cryptocurrency', 10, 1800.00, '$', 'create', '2023-10-10', 'Global', '2023-10-10 10:00:00', sample_user_id),
        (asset_id_5, bucket_id_2, 'Ethereum', 'ETH', 'Cryptocurrency', 10, 2000.00, '$', 'update', '2023-11-10', 'Global', '2023-11-10 10:00:00', sample_user_id),
        (asset_id_5, bucket_id_2, 'Ethereum', 'ETH', 'Cryptocurrency', 10, 2200.00, '$', 'update', '2023-12-10', 'Global', '2023-12-10 10:00:00', sample_user_id),
        (asset_id_5, bucket_id_2, 'Ethereum', 'ETH', 'Cryptocurrency', 10, 2350.00, '$', 'update', '2024-01-10', 'Global', '2024-01-10 10:00:00', sample_user_id),
        (asset_id_5, bucket_id_2, 'Ethereum', 'ETH', 'Cryptocurrency', 10, 2450.00, '$', 'update', '2024-02-10', 'Global', '2024-02-10 10:00:00', sample_user_id);
    
    -- UK Property
    asset_id_6 := gen_random_uuid();
    INSERT INTO assets (asset_identifier, bucket_id, name, symbol, type, quantity, unit_price, currency_symbol, snapshot_type, holding_date, country, created_at, user_id) VALUES
        (asset_id_6, bucket_id_1, 'London Property', 'PROP-UK', 'Real Estate', 1, 450000.00, '£', 'create', '2023-06-01', 'UK', '2023-06-01 10:00:00', sample_user_id),
        (asset_id_6, bucket_id_1, 'London Property', 'PROP-UK', 'Real Estate', 1, 465000.00, '£', 'update', '2024-01-01', 'UK', '2024-01-01 10:00:00', sample_user_id);
    
    -- Japanese Stock
    asset_id_7 := gen_random_uuid();
    INSERT INTO assets (asset_identifier, bucket_id, name, symbol, type, quantity, unit_price, currency_symbol, snapshot_type, holding_date, country, created_at, user_id) VALUES
        (asset_id_7, bucket_id_1, 'Toyota Motor Corp', 'TM', 'Stock', 200, 15000.00, '¥', 'create', '2024-01-05', 'Japan', '2024-01-05 10:00:00', sample_user_id),
        (asset_id_7, bucket_id_1, 'Toyota Motor Corp', 'TM', 'Stock', 200, 15750.00, '¥', 'update', '2024-02-05', 'Japan', '2024-02-05 10:00:00', sample_user_id);

END $$;
