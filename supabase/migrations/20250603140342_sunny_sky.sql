/*
  # Reset database and recreate schema

  1. Changes
    - Drop existing tables
    - Recreate tables with proper structure
    - Set up RLS policies
    
  2. Security
    - Enable RLS on all tables
    - Add comprehensive policies for authenticated users
*/

-- Drop existing tables if they exist
DROP TABLE IF EXISTS assets CASCADE;
DROP TABLE IF EXISTS buckets CASCADE;

-- Create buckets table
CREATE TABLE buckets (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  created_at timestamptz DEFAULT now(),
  user_id uuid REFERENCES auth.users NOT NULL
);

-- Create assets table
CREATE TABLE assets (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  bucket_id uuid REFERENCES buckets ON DELETE CASCADE NOT NULL,
  name text NOT NULL,
  symbol text NOT NULL,
  type text NOT NULL,
  quantity numeric NOT NULL,
  unit_price numeric NOT NULL,
  created_at timestamptz DEFAULT now(),
  user_id uuid REFERENCES auth.users NOT NULL
);

-- Enable RLS
ALTER TABLE buckets ENABLE ROW LEVEL SECURITY;
ALTER TABLE assets ENABLE ROW LEVEL SECURITY;

-- Bucket policies
CREATE POLICY "Users can view their own buckets"
  ON buckets
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create buckets"
  ON buckets
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own buckets"
  ON buckets
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own buckets"
  ON buckets
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Asset policies
CREATE POLICY "Users can view their own assets"
  ON assets
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create assets"
  ON assets
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own assets"
  ON assets
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own assets"
  ON assets
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);