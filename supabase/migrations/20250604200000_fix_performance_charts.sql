/*
  # Fix portfolio performance charts and data aggregation

  1. Changes
    - Fix portfolio_value_at_date to handle multiple assets properly
    - Create better aggregated views for chart data
    - Ensure proper date handling for different timeframes

  2. Performance Tracking Logic
    - Aggregate portfolio value by date properly
    - Handle timeframe filtering correctly
    - Fix duplicate date issues
*/

-- Drop dependent views first
DROP VIEW IF EXISTS portfolio_timeframe_data;
DROP VIEW IF EXISTS portfolio_performance_timeframes;
DROP VIEW IF EXISTS portfolio_performance_history;
DROP VIEW IF EXISTS monthly_portfolio_performance;
DROP VIEW IF EXISTS portfolio_value_at_date;

CREATE OR REPLACE VIEW portfolio_value_at_date AS
WITH latest_asset_values AS (
  -- For each holding_date, get the latest snapshot of each asset up to that date
  SELECT DISTINCT ON (user_id, holding_date, asset_identifier)
    user_id,
    asset_identifier,
    holding_date,
    quantity,
    unit_price,
    currency_symbol,
    snapshot_type,
    country,
    created_at
  FROM assets
  WHERE holding_date IS NOT NULL
  ORDER BY user_id, holding_date, asset_identifier, created_at DESC
)
SELECT 
  user_id,
  holding_date as value_date,
  SUM(CASE WHEN snapshot_type != 'delete' THEN quantity * COALESCE(unit_price, 0) ELSE 0 END) as portfolio_value,
  COUNT(DISTINCT CASE WHEN snapshot_type != 'delete' THEN asset_identifier END) as active_assets,
  ARRAY_AGG(DISTINCT country) FILTER (WHERE country IS NOT NULL AND snapshot_type != 'delete') as countries
FROM latest_asset_values
GROUP BY user_id, holding_date
ORDER BY user_id, holding_date;

-- Grant access to the view
GRANT SELECT ON portfolio_value_at_date TO authenticated;

-- Create a simplified chart data view
CREATE OR REPLACE VIEW portfolio_chart_data AS
WITH monthly_aggregated AS (
  -- Aggregate by month for better chart performance
  SELECT 
    user_id,
    DATE_TRUNC('month', value_date) as chart_date,
    'monthly' as period_type,
    AVG(portfolio_value) as avg_value,
    MAX(portfolio_value) as max_value,
    MIN(portfolio_value) as min_value,
    COUNT(*) as data_points
  FROM portfolio_value_at_date
  GROUP BY user_id, DATE_TRUNC('month', value_date)
),
daily_recent AS (
  -- Keep daily data for recent periods (last 3 months)
  SELECT 
    user_id,
    value_date as chart_date,
    'daily' as period_type,
    portfolio_value as avg_value,
    portfolio_value as max_value,
    portfolio_value as min_value,
    1 as data_points
  FROM portfolio_value_at_date
  WHERE value_date >= CURRENT_DATE - INTERVAL '3 months'
)
SELECT * FROM monthly_aggregated
UNION ALL
SELECT * FROM daily_recent
ORDER BY chart_date;

-- Grant access to the view
GRANT SELECT ON portfolio_chart_data TO authenticated;

-- Create timeframe-specific views
CREATE OR REPLACE VIEW portfolio_timeframe_data AS
WITH base_data AS (
  SELECT 
    user_id,
    chart_date,
    avg_value as portfolio_value,
    CASE 
      WHEN chart_date >= CURRENT_DATE - INTERVAL '1 month' THEN '1M'
      WHEN chart_date >= CURRENT_DATE - INTERVAL '6 months' THEN '6M'
      WHEN chart_date >= CURRENT_DATE - INTERVAL '1 year' THEN '1Y'
      WHEN chart_date >= CURRENT_DATE - INTERVAL '5 years' THEN '5Y'
      ELSE NULL
    END as timeframe
  FROM portfolio_chart_data
  WHERE chart_date >= CURRENT_DATE - INTERVAL '5 years'
)
SELECT 
  user_id,
  timeframe,
  chart_date as performance_date,
  portfolio_value as total_value,
  CASE 
    WHEN timeframe = '1M' THEN 
      CASE 
        WHEN EXTRACT(DAY FROM chart_date) <= 7 THEN 'Week 1'
        WHEN EXTRACT(DAY FROM chart_date) <= 14 THEN 'Week 2'
        WHEN EXTRACT(DAY FROM chart_date) <= 21 THEN 'Week 3'
        ELSE 'Week 4'
      END
    WHEN timeframe IN ('6M', '1Y') THEN TO_CHAR(chart_date, 'Mon YYYY')
    WHEN timeframe = '5Y' THEN EXTRACT(YEAR FROM chart_date)::text
    ELSE TO_CHAR(chart_date, 'YYYY-MM-DD')
  END as display_label
FROM base_data
WHERE timeframe IS NOT NULL
ORDER BY timeframe, performance_date;

-- Grant access to the view
GRANT SELECT ON portfolio_timeframe_data TO authenticated;
