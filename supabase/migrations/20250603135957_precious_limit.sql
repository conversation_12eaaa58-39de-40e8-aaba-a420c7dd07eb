/*
  # Fix Assets RLS Policies

  1. Changes
    - Add RLS policies for assets table to allow users to:
      - Update their own assets (quantity and unit price)
      - Insert new assets
      - Delete their own assets
      - View their own assets

  2. Security
    - Enable RLS on assets table
    - Add policies for authenticated users to:
      - Insert assets they own
      - Update assets they own
      - Delete assets they own
      - View assets they own
*/

-- First, ensure RLS is enabled
ALTER TABLE assets ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can create assets" ON assets;
DROP POLICY IF EXISTS "Users can update their own assets" ON assets;
DROP POLICY IF EXISTS "Users can delete their own assets" ON assets;
DROP POLICY IF EXISTS "Users can view their own assets" ON assets;

-- Create comprehensive RLS policies
CREATE POLICY "Users can create assets"
ON assets
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own assets"
ON assets
FOR UPDATE
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own assets"
ON assets
FOR DELETE
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own assets"
ON assets
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);