/*
  # Fix Assets Table RLS Policies

  1. Changes
    - Drop existing RLS policies for assets table
    - Create new comprehensive RLS policies for assets table that properly handle all operations
    
  2. Security
    - Enable RLS on assets table
    - Add policies for:
      - SELECT: Users can view their own assets
      - INSERT: Users can create assets with their user_id
      - UPDATE: Users can update their own assets
      - DELETE: Users can delete their own assets
    - All policies ensure users can only access their own data
*/

-- First, drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can create assets" ON assets;
DROP POLICY IF EXISTS "Users can delete their own assets" ON assets;
DROP POLICY IF EXISTS "Users can insert their own assets" ON assets;
DROP POLICY IF EXISTS "Users can update their own assets" ON assets;
DROP POLICY IF EXISTS "Users can view their own assets" ON assets;

-- Ensure RLS is enabled
ALTER TABLE assets ENABLE ROW LEVEL SECURITY;

-- Create new policies
CREATE POLICY "Enable read access for own assets"
    ON assets
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Enable insert access for own assets"
    ON assets
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable update access for own assets"
    ON assets
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable delete access for own assets"
    ON assets
    FOR DELETE
    TO authenticated
    USING (auth.uid() = user_id);