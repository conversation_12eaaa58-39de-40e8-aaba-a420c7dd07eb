/*
  # Fix chart axis labels and remove duplicates

  1. Problem
    - Chart axis showing duplicate labels (Nov Nov, Dec Dec, etc.)
    - Incorrect date formatting for different timeframes
    - Multiple data points for same period causing duplicates

  2. Solution
    - Aggregate data properly to avoid duplicates
    - Fix date formatting for each timeframe
    - Ensure one data point per period
*/

-- Drop and recreate the timeframe view with proper aggregation
DROP VIEW IF EXISTS portfolio_timeframe_data;

CREATE OR REPLACE VIEW portfolio_timeframe_data AS
WITH base_data AS (
  SELECT 
    user_id,
    value_date,
    portfolio_value
  FROM portfolio_value_evolution
),
-- Aggregate data by different periods to avoid duplicates
monthly_data AS (
  SELECT DISTINCT ON (user_id, DATE_TRUNC('month', value_date))
    user_id,
    DATE_TRUNC('month', value_date) as period_start,
    value_date as latest_date_in_period,
    portfolio_value as period_value
  FROM base_data
  ORDER BY user_id, DATE_TRUNC('month', value_date), value_date DESC
),
yearly_data AS (
  SELECT DISTINCT ON (user_id, DATE_TRUNC('year', value_date))
    user_id,
    DATE_TRUNC('year', value_date) as period_start,
    value_date as latest_date_in_period,
    portfolio_value as period_value
  FROM base_data
  ORDER BY user_id, DATE_TRUNC('year', value_date), value_date DESC
),
-- Create timeframe-specific data
timeframe_1m AS (
  SELECT
    user_id,
    '1M' as timeframe,
    value_date as performance_date,
    portfolio_value as total_value,
    TO_CHAR(value_date, 'Mon DD') as display_label
  FROM base_data
  WHERE value_date >= (SELECT MAX(value_date) FROM base_data) - INTERVAL '1 month'
),
timeframe_6m AS (
  SELECT
    user_id,
    '6M' as timeframe,
    latest_date_in_period as performance_date,
    period_value as total_value,
    TO_CHAR(period_start, 'Mon') as display_label
  FROM monthly_data
  WHERE period_start >= (SELECT MAX(latest_date_in_period) FROM monthly_data) - INTERVAL '6 months'
),
timeframe_1y AS (
  SELECT
    user_id,
    '1Y' as timeframe,
    latest_date_in_period as performance_date,
    period_value as total_value,
    TO_CHAR(period_start, 'Mon YY') as display_label
  FROM monthly_data
  WHERE period_start >= (SELECT MAX(latest_date_in_period) FROM monthly_data) - INTERVAL '1 year'
),
timeframe_5y AS (
  SELECT
    user_id,
    '5Y' as timeframe,
    latest_date_in_period as performance_date,
    period_value as total_value,
    EXTRACT(YEAR FROM period_start)::text as display_label
  FROM yearly_data
  WHERE period_start >= (SELECT MAX(latest_date_in_period) FROM yearly_data) - INTERVAL '5 years'
)
-- Combine all timeframes
SELECT * FROM timeframe_1m
UNION ALL
SELECT * FROM timeframe_6m
UNION ALL
SELECT * FROM timeframe_1y
UNION ALL
SELECT * FROM timeframe_5y
ORDER BY timeframe, performance_date;

-- Grant access to the view
GRANT SELECT ON portfolio_timeframe_data TO authenticated;

-- Also create a simplified chart view that ensures no duplicates
CREATE OR REPLACE VIEW portfolio_chart_clean AS
WITH deduplicated_data AS (
  SELECT DISTINCT ON (user_id, timeframe, display_label)
    user_id,
    timeframe,
    performance_date,
    total_value,
    display_label
  FROM portfolio_timeframe_data
  ORDER BY user_id, timeframe, display_label, performance_date DESC
)
SELECT 
  user_id,
  timeframe,
  performance_date,
  total_value,
  display_label,
  ROW_NUMBER() OVER (PARTITION BY user_id, timeframe ORDER BY performance_date) as sequence_number
FROM deduplicated_data
ORDER BY timeframe, performance_date;

-- Grant access to the clean chart view
GRANT SELECT ON portfolio_chart_clean TO authenticated;
