/*
  # Fix Bucket RLS Policies

  1. Changes
    - Drop existing RLS policies for buckets table
    - Create new RLS policies with proper user_id checks
    
  2. Security
    - Enable RLS on buckets table
    - Add policies for authenticated users to:
      - Insert new buckets (with user_id check)
      - Select their own buckets
      - Update their own buckets
      - Delete their own buckets
*/

-- First enable RLS if not already enabled
ALTER TABLE buckets ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can create buckets" ON buckets;
DROP POLICY IF EXISTS "Users can view their own buckets" ON buckets;
DROP POLICY IF EXISTS "Users can update their own buckets" ON buckets;
DROP POLICY IF EXISTS "Users can delete their own buckets" ON buckets;

-- Create new policies with proper user_id checks
CREATE POLICY "Users can create buckets"
ON buckets
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own buckets"
ON buckets
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own buckets"
ON buckets
FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own buckets"
ON buckets
FOR DELETE
TO authenticated
USING (auth.uid() = user_id);