/*
  # Fix Asset RLS Policies

  1. Changes
    - Drop existing RLS policies for assets table
    - Create new RLS policies with proper user_id checks
    
  2. Security
    - Enable RLS on assets table
    - Add policies for authenticated users to:
      - Insert new assets (with user_id check)
      - Update their own assets
*/

-- First enable RLS if not already enabled
ALTER TABLE assets ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can update their own assets" ON assets;
DROP POLICY IF EXISTS "Users can insert their own assets" ON assets;

-- Create new policies with proper user_id checks
CREATE POLICY "Users can update their own assets"
ON assets
FOR UPDATE
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can insert their own assets"
ON assets
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);