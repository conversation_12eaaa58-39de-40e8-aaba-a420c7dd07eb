/*
  # Clean up unused database views

  1. Analysis of Current Views
    - portfolio_value_evolution: KEEP (core portfolio tracking)
    - portfolio_chart_clean: KEEP (used by PriceChart component)
    - portfolio_timeframe_data: KEEP (used by PriceChart component)
    - latest_assets: KEEP (used by Dashboard and other components)
    - monthly_portfolio_performance: REMOVE (replaced by portfolio_chart_clean)
    - portfolio_performance_history: REMOVE (replaced by portfolio_value_evolution)
    - portfolio_performance_timeframes: REMOVE (replaced by portfolio_timeframe_data)
    - portfolio_chart_fallback: REMOVE (not used anymore)
    - portfolio_chart_data: REMOVE (replaced by portfolio_chart_clean)
    - portfolio_value_at_date: REMOVE (replaced by portfolio_value_evolution)

  2. Strategy
    - Keep only the views that are actively used by the application
    - Remove redundant and obsolete views
    - Maintain clean database schema
*/

-- Drop all unused/redundant views
DROP VIEW IF EXISTS monthly_portfolio_performance CASCADE;
DROP VIEW IF EXISTS portfolio_performance_history CASCADE;
DROP VIEW IF EXISTS portfolio_performance_timeframes CASCADE;
DROP VIEW IF EXISTS portfolio_chart_fallback CASCADE;
DROP VIEW IF EXISTS portfolio_chart_data CASCADE;
DROP VIEW IF EXISTS portfolio_value_at_date CASCADE;

-- Keep these essential views:
-- 1. latest_assets (used by Dashboard, AllAssets, CountryDistribution)
-- 2. portfolio_value_evolution (core portfolio tracking)
-- 3. portfolio_timeframe_data (used by PriceChart)
-- 4. portfolio_chart_clean (used by PriceChart)

-- Verify the remaining views are properly accessible
GRANT SELECT ON latest_assets TO authenticated;
GRANT SELECT ON portfolio_value_evolution TO authenticated;
GRANT SELECT ON portfolio_timeframe_data TO authenticated;
GRANT SELECT ON portfolio_chart_clean TO authenticated;

-- Add comments to document the purpose of each remaining view
COMMENT ON VIEW latest_assets IS 'Shows the latest snapshot of each asset for current portfolio state';
COMMENT ON VIEW portfolio_value_evolution IS 'Core view tracking portfolio value evolution over time based on holding_date';
COMMENT ON VIEW portfolio_timeframe_data IS 'Provides portfolio data formatted for different chart timeframes (1M, 6M, 1Y, 5Y)';
COMMENT ON VIEW portfolio_chart_clean IS 'Clean, deduplicated chart data with proper sequencing for frontend consumption';
