/*
  # Create buckets and assets tables

  1. New Tables
    - `buckets`
      - `id` (uuid, primary key)
      - `name` (text, unique)
      - `created_at` (timestamp)
      - `user_id` (uuid, foreign key to auth.users)
    - `assets`
      - `id` (uuid, primary key)
      - `bucket_id` (uuid, foreign key to buckets)
      - `name` (text)
      - `symbol` (text)
      - `type` (text)
      - `old_quantity` (numeric)
      - `old_unit_price` (numeric)
      - `new_quantity` (numeric)
      - `new_unit_price` (numeric)
      - `status` (text)
      - `created_at` (timestamp)
      - `user_id` (uuid, foreign key to auth.users)

  2. Security
    - Enable RLS on both tables
    - Add policies for authenticated users to manage their own data
*/

-- Create buckets table
CREATE TABLE buckets (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  created_at timestamptz DEFAULT now(),
  user_id uuid REFERENCES auth.users NOT NULL
);

-- Create assets table
CREATE TABLE assets (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  bucket_id uuid REFERENCES buckets ON DELETE CASCADE NOT NULL,
  name text NOT NULL,
  symbol text NOT NULL,
  type text NOT NULL,
  old_quantity numeric NOT NULL DEFAULT 0,
  old_unit_price numeric NOT NULL DEFAULT 0,
  new_quantity numeric,
  new_unit_price numeric,
  status text NOT NULL DEFAULT 'existing',
  created_at timestamptz DEFAULT now(),
  user_id uuid REFERENCES auth.users NOT NULL
);

-- Enable RLS
ALTER TABLE buckets ENABLE ROW LEVEL SECURITY;
ALTER TABLE assets ENABLE ROW LEVEL SECURITY;

-- Bucket policies
CREATE POLICY "Users can view their own buckets"
  ON buckets
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create buckets"
  ON buckets
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own buckets"
  ON buckets
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own buckets"
  ON buckets
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Asset policies
CREATE POLICY "Users can view their own assets"
  ON assets
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create assets"
  ON assets
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own assets"
  ON assets
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own assets"
  ON assets
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);