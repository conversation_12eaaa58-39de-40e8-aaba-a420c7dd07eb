/*
  # Update all views to use the new currencies table

  1. Changes
    - Update latest_assets view to include currency information
    - Update available_currencies view to use currencies table
    - Update portfolio_by_currency view with proper currency data
    - Update asset_distribution_by_currency view
    - Add currency conversion functionality to portfolio views

  2. Security
    - All views inherit RLS from underlying tables
*/

-- Drop dependent views first to avoid dependency issues
DROP VIEW IF EXISTS available_currencies;
DROP VIEW IF EXISTS portfolio_by_currency;
DROP VIEW IF EXISTS asset_distribution_by_currency;

-- Drop and recreate latest_assets view to include currency information
DROP VIEW IF EXISTS latest_assets;

CREATE VIEW latest_assets AS
SELECT DISTINCT ON (a.asset_identifier)
  a.id,
  a.asset_identifier,
  a.bucket_id,
  a.name,
  a.symbol,
  a.type,
  a.quantity,
  a.unit_price,
  a.currency_symbol,
  a.currency_code,
  c.symbol as currency_symbol_from_table,
  c.name as currency_name,
  c.country_code,
  c.exchange_rate_to_usd,
  a.snapshot_type,
  a.holding_date,
  a.country,
  a.created_at,
  a.user_id
FROM assets a
LEFT JOIN currencies c ON a.currency_code = c.code
ORDER BY a.asset_identifier, a.created_at DESC;

-- Grant access to the view
GRANT SELECT ON latest_assets TO authenticated;

-- Create available_currencies view to use currencies table
CREATE OR REPLACE VIEW available_currencies AS
SELECT DISTINCT 
  la.user_id,
  la.currency_code,
  c.symbol as currency_symbol,
  c.name as currency_name,
  c.country_code,
  c.exchange_rate_to_usd,
  COUNT(*) as asset_count
FROM latest_assets la
JOIN currencies c ON la.currency_code = c.code
WHERE la.snapshot_type != 'delete'
GROUP BY la.user_id, la.currency_code, c.symbol, c.name, c.country_code, c.exchange_rate_to_usd
ORDER BY la.user_id, asset_count DESC;

-- Grant access to the view
GRANT SELECT ON available_currencies TO authenticated;

-- Create portfolio_by_currency view to use currencies table
CREATE OR REPLACE VIEW portfolio_by_currency AS
WITH currency_totals AS (
  SELECT 
    la.user_id,
    la.currency_code,
    c.symbol as currency_symbol,
    c.name as currency_name,
    c.country_code,
    c.exchange_rate_to_usd,
    SUM(la.quantity * la.unit_price) as total_value,
    SUM(la.quantity * la.unit_price * c.exchange_rate_to_usd) as total_value_usd,
    COUNT(*) as asset_count,
    ARRAY_AGG(DISTINCT la.country) FILTER (WHERE la.country IS NOT NULL) as countries
  FROM latest_assets la
  JOIN currencies c ON la.currency_code = c.code
  WHERE la.snapshot_type != 'delete'
  GROUP BY la.user_id, la.currency_code, c.symbol, c.name, c.country_code, c.exchange_rate_to_usd
)
SELECT 
  user_id,
  currency_code,
  currency_symbol,
  currency_name,
  country_code,
  exchange_rate_to_usd,
  total_value,
  total_value_usd,
  asset_count,
  countries,
  -- Calculate percentage of total portfolio (in USD)
  ROUND(
    (total_value_usd / SUM(total_value_usd) OVER (PARTITION BY user_id) * 100)::numeric, 
    2
  ) as portfolio_percentage
FROM currency_totals
ORDER BY user_id, total_value_usd DESC;

-- Grant access to the view
GRANT SELECT ON portfolio_by_currency TO authenticated;

-- Create asset_distribution_by_currency view to use currencies table
CREATE OR REPLACE VIEW asset_distribution_by_currency AS
WITH currency_type_totals AS (
  SELECT
    la.user_id,
    la.currency_code,
    c.symbol as currency_symbol,
    c.name as currency_name,
    c.country_code,
    c.exchange_rate_to_usd,
    la.type,
    SUM(la.quantity * la.unit_price) as type_value,
    SUM(la.quantity * la.unit_price * c.exchange_rate_to_usd) as type_value_usd,
    COUNT(*) as type_count
  FROM latest_assets la
  JOIN currencies c ON la.currency_code = c.code
  WHERE la.snapshot_type != 'delete'
  GROUP BY la.user_id, la.currency_code, c.symbol, c.name, c.country_code, c.exchange_rate_to_usd, la.type
)
SELECT
  user_id,
  currency_code,
  currency_symbol,
  currency_name,
  country_code,
  exchange_rate_to_usd,
  type,
  type_value,
  type_value_usd,
  type_count,
  -- Calculate percentage within currency
  ROUND(
    (type_value / SUM(type_value) OVER (PARTITION BY user_id, currency_code) * 100)::numeric,
    2
  ) as currency_percentage,
  -- Calculate percentage of total portfolio (in USD)
  ROUND(
    (type_value_usd / SUM(type_value_usd) OVER (PARTITION BY user_id) * 100)::numeric,
    2
  ) as portfolio_percentage
FROM currency_type_totals
ORDER BY user_id, currency_code, type_value_usd DESC;

-- Grant access to the view
GRANT SELECT ON asset_distribution_by_currency TO authenticated;

-- Add comments to document the updated views
COMMENT ON VIEW available_currencies IS 'Shows distinct currencies available in each user portfolio with exchange rates from currencies table';
COMMENT ON VIEW portfolio_by_currency IS 'Portfolio totals grouped by currency with USD conversion using exchange rates';
COMMENT ON VIEW asset_distribution_by_currency IS 'Asset type distribution within each currency and overall portfolio with USD conversion';
