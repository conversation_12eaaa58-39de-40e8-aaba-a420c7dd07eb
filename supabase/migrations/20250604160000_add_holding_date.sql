/*
  # Add holding_date column to assets table (Value As On date)

  1. Changes
    - Add holding_date column to assets table (stores "Value As On" date)
    - Update latest_assets view to include holding_date

  2. Security
    - RLS policies remain unchanged as they are table-level
*/

-- Add holding_date column to assets table (Value As On date)
ALTER TABLE assets
  ADD COLUMN IF NOT EXISTS holding_date date;

-- Drop and recreate the latest_assets view to include holding_date
DROP VIEW IF EXISTS latest_assets;

CREATE VIEW latest_assets AS
SELECT DISTINCT ON (asset_identifier)
  id,
  asset_identifier,
  bucket_id,
  name,
  symbol,
  type,
  quantity,
  unit_price,
  currency_symbol,
  snapshot_type,
  holding_date,
  created_at,
  user_id
FROM assets
ORDER BY asset_identifier, created_at DESC;

-- Grant access to the view
GRANT SELECT ON latest_assets TO authenticated;

-- Note: Views inherit RLS from underlying tables
-- The latest_assets view will automatically respect the RLS policies on the assets table
