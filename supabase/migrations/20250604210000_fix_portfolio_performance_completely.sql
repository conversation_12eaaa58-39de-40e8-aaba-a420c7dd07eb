/*
  # Completely fix portfolio performance tracking

  1. Problem Analysis
    - Current views incorrectly aggregate portfolio values
    - Need to track portfolio value evolution over time
    - Must consider latest asset values up to each date

  2. Correct Logic
    - For any given date, portfolio value = sum of (latest quantity * latest price) for each asset up to that date
    - Track how portfolio grows as new assets are added and existing ones are updated
    - Handle deletions properly (quantity = 0)

  3. New Approach
    - Create a proper time-series view that shows portfolio evolution
    - Use window functions to get latest asset values up to each date
    - Aggregate correctly for chart display
*/

-- Drop all existing performance views
DROP VIEW IF EXISTS portfolio_timeframe_data CASCADE;
DROP VIEW IF EXISTS portfolio_chart_data CASCADE;
DROP VIEW IF EXISTS portfolio_performance_timeframes CASCADE;
DROP VIEW IF EXISTS portfolio_performance_history CASCADE;
DROP VIEW IF EXISTS monthly_portfolio_performance CASCADE;
DROP VIEW IF EXISTS portfolio_value_at_date CASCADE;

-- Create the fundamental view: portfolio value evolution over time
CREATE OR REPLACE VIEW portfolio_value_evolution AS
WITH all_dates AS (
  -- Get all unique holding dates to create a timeline
  SELECT DISTINCT 
    user_id,
    holding_date as value_date
  FROM assets 
  WHERE holding_date IS NOT NULL
),
asset_values_at_date AS (
  -- For each date and asset, get the latest value up to that date
  SELECT 
    ad.user_id,
    ad.value_date,
    a.asset_identifier,
    a.name,
    a.symbol,
    a.type,
    a.country,
    -- Get the latest snapshot for this asset up to this date
    FIRST_VALUE(a.quantity) OVER (
      PARTITION BY ad.user_id, ad.value_date, a.asset_identifier 
      ORDER BY a.holding_date DESC, a.created_at DESC
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) as latest_quantity,
    FIRST_VALUE(a.unit_price) OVER (
      PARTITION BY ad.user_id, ad.value_date, a.asset_identifier 
      ORDER BY a.holding_date DESC, a.created_at DESC
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) as latest_unit_price,
    FIRST_VALUE(a.currency_symbol) OVER (
      PARTITION BY ad.user_id, ad.value_date, a.asset_identifier 
      ORDER BY a.holding_date DESC, a.created_at DESC
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) as latest_currency,
    FIRST_VALUE(a.snapshot_type) OVER (
      PARTITION BY ad.user_id, ad.value_date, a.asset_identifier 
      ORDER BY a.holding_date DESC, a.created_at DESC
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) as latest_snapshot_type,
    ROW_NUMBER() OVER (
      PARTITION BY ad.user_id, ad.value_date, a.asset_identifier 
      ORDER BY a.holding_date DESC, a.created_at DESC
    ) as rn
  FROM all_dates ad
  JOIN assets a ON a.user_id = ad.user_id 
    AND a.holding_date <= ad.value_date
  WHERE a.holding_date IS NOT NULL
),
portfolio_at_each_date AS (
  -- Calculate portfolio value at each date
  SELECT 
    user_id,
    value_date,
    SUM(
      CASE 
        WHEN latest_snapshot_type != 'delete' AND latest_unit_price IS NOT NULL 
        THEN latest_quantity * latest_unit_price 
        ELSE 0 
      END
    ) as portfolio_value,
    COUNT(
      CASE 
        WHEN latest_snapshot_type != 'delete' AND latest_unit_price IS NOT NULL 
        THEN 1 
      END
    ) as active_assets,
    ARRAY_AGG(
      DISTINCT country 
      ORDER BY country
    ) FILTER (
      WHERE latest_snapshot_type != 'delete' 
        AND latest_unit_price IS NOT NULL 
        AND country IS NOT NULL
    ) as countries
  FROM asset_values_at_date
  WHERE rn = 1  -- Only take the latest value for each asset at each date
  GROUP BY user_id, value_date
)
SELECT 
  user_id,
  value_date,
  portfolio_value,
  active_assets,
  countries,
  LAG(portfolio_value) OVER (
    PARTITION BY user_id 
    ORDER BY value_date
  ) as previous_value,
  CASE 
    WHEN LAG(portfolio_value) OVER (PARTITION BY user_id ORDER BY value_date) > 0 THEN 
      ROUND(
        ((portfolio_value - LAG(portfolio_value) OVER (PARTITION BY user_id ORDER BY value_date)) / 
         LAG(portfolio_value) OVER (PARTITION BY user_id ORDER BY value_date) * 100)::numeric, 
        2
      )
    ELSE 0 
  END as daily_change_percentage
FROM portfolio_at_each_date
ORDER BY user_id, value_date;

-- Grant access to the view
GRANT SELECT ON portfolio_value_evolution TO authenticated;

-- Create monthly aggregated view
CREATE OR REPLACE VIEW monthly_portfolio_performance AS
WITH monthly_data AS (
  SELECT 
    user_id,
    DATE_TRUNC('month', value_date) as month,
    -- Get the last portfolio value of each month
    LAST_VALUE(portfolio_value) OVER (
      PARTITION BY user_id, DATE_TRUNC('month', value_date)
      ORDER BY value_date
      ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) as month_end_value,
    MAX(value_date) OVER (
      PARTITION BY user_id, DATE_TRUNC('month', value_date)
    ) as last_date_in_month,
    ROW_NUMBER() OVER (
      PARTITION BY user_id, DATE_TRUNC('month', value_date)
      ORDER BY value_date DESC
    ) as rn
  FROM portfolio_value_evolution
)
SELECT 
  user_id,
  month,
  month_end_value as avg_monthly_value,
  month_end_value as max_monthly_value,
  month_end_value as min_monthly_value,
  last_date_in_month,
  LAG(month_end_value) OVER (PARTITION BY user_id ORDER BY month) as previous_month_value,
  CASE 
    WHEN LAG(month_end_value) OVER (PARTITION BY user_id ORDER BY month) > 0 THEN 
      ROUND(((month_end_value - LAG(month_end_value) OVER (PARTITION BY user_id ORDER BY month)) / 
             LAG(month_end_value) OVER (PARTITION BY user_id ORDER BY month) * 100)::numeric, 2)
    ELSE 0 
  END as monthly_change_percentage
FROM monthly_data
WHERE rn = 1
ORDER BY user_id, month;

-- Grant access to the view
GRANT SELECT ON monthly_portfolio_performance TO authenticated;

-- Create timeframe-specific view for charts
CREATE OR REPLACE VIEW portfolio_timeframe_data AS
WITH timeframe_data AS (
  SELECT 
    user_id,
    value_date as performance_date,
    portfolio_value as total_value,
    CASE 
      WHEN value_date >= CURRENT_DATE - INTERVAL '1 month' THEN '1M'
      WHEN value_date >= CURRENT_DATE - INTERVAL '6 months' THEN '6M'
      WHEN value_date >= CURRENT_DATE - INTERVAL '1 year' THEN '1Y'
      WHEN value_date >= CURRENT_DATE - INTERVAL '5 years' THEN '5Y'
      ELSE NULL
    END as timeframe
  FROM portfolio_value_evolution
  WHERE value_date >= CURRENT_DATE - INTERVAL '5 years'
)
SELECT 
  user_id,
  timeframe,
  performance_date,
  total_value,
  CASE 
    WHEN timeframe = '1M' THEN 
      TO_CHAR(performance_date, 'Mon DD')
    WHEN timeframe IN ('6M', '1Y') THEN 
      TO_CHAR(performance_date, 'Mon YY')
    WHEN timeframe = '5Y' THEN 
      EXTRACT(YEAR FROM performance_date)::text
    ELSE TO_CHAR(performance_date, 'YYYY-MM-DD')
  END as display_label
FROM timeframe_data
WHERE timeframe IS NOT NULL
ORDER BY timeframe, performance_date;

-- Grant access to the view
GRANT SELECT ON portfolio_timeframe_data TO authenticated;
