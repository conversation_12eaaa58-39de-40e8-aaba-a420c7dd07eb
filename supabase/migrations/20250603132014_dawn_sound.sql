/*
  # Simplify assets table

  1. Changes
    - Remove old_quantity and old_unit_price columns
    - Rename new_quantity to quantity
    - Rename new_unit_price to unit_price
    - Remove status column as we'll only store current data

  2. Security
    - RLS policies remain unchanged
*/

-- Modify assets table
ALTER TABLE assets 
  DROP COLUMN old_quantity,
  DROP COLUMN old_unit_price,
  DROP COLUMN status;

ALTER TABLE assets 
  RENAME COLUMN new_quantity TO quantity;

ALTER TABLE assets 
  RENAME COLUMN new_unit_price TO unit_price;

-- Make quantity and unit_price NOT NULL
ALTER TABLE assets 
  ALTER COLUMN quantity SET NOT NULL,
  ALTER COLUMN unit_price SET NOT NULL;