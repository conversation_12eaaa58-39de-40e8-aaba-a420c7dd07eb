/*
  # Fix 1M chart to show proper recent data

  1. Problem
    - 1M chart showing data from multiple months
    - Data not in chronological order
    - Using CURRENT_DATE which may not match actual data dates

  2. Solution
    - Use the latest data date as reference point
    - Show only the most recent data points
    - Ensure proper chronological ordering
*/

-- Drop and recreate with better 1M logic
DROP VIEW IF EXISTS portfolio_chart_clean;
DROP VIEW IF EXISTS portfolio_timeframe_data;

CREATE OR REPLACE VIEW portfolio_timeframe_data AS
WITH base_data AS (
  SELECT 
    user_id,
    value_date,
    portfolio_value
  FROM portfolio_value_evolution
),
-- Get the latest date in the data to use as reference
latest_data_info AS (
  SELECT 
    user_id,
    MAX(value_date) as latest_date
  FROM base_data
  GROUP BY user_id
),
-- Aggregate data by different periods to avoid duplicates
monthly_data AS (
  SELECT DISTINCT ON (user_id, DATE_TRUNC('month', value_date))
    user_id,
    DATE_TRUNC('month', value_date) as period_start,
    value_date as latest_date_in_period,
    portfolio_value as period_value
  FROM base_data
  ORDER BY user_id, DATE_TRUNC('month', value_date), value_date DESC
),
yearly_data AS (
  SELECT DISTINCT ON (user_id, DATE_TRUNC('year', value_date))
    user_id,
    DATE_TRUNC('year', value_date) as period_start,
    value_date as latest_date_in_period,
    portfolio_value as period_value
  FROM base_data
  ORDER BY user_id, DATE_TRUNC('year', value_date), value_date DESC
),
-- Create timeframe-specific data
timeframe_1m AS (
  SELECT 
    bd.user_id,
    '1M' as timeframe,
    bd.value_date as performance_date,
    bd.portfolio_value as total_value,
    TO_CHAR(bd.value_date, 'Mon DD') as display_label
  FROM base_data bd
  JOIN latest_data_info ldi ON bd.user_id = ldi.user_id
  WHERE bd.value_date >= ldi.latest_date - INTERVAL '1 month'
),
timeframe_6m AS (
  SELECT 
    md.user_id,
    '6M' as timeframe,
    md.latest_date_in_period as performance_date,
    md.period_value as total_value,
    TO_CHAR(md.period_start, 'Mon') as display_label
  FROM monthly_data md
  JOIN latest_data_info ldi ON md.user_id = ldi.user_id
  WHERE md.period_start >= ldi.latest_date - INTERVAL '6 months'
),
timeframe_1y AS (
  SELECT 
    md.user_id,
    '1Y' as timeframe,
    md.latest_date_in_period as performance_date,
    md.period_value as total_value,
    TO_CHAR(md.period_start, 'Mon YY') as display_label
  FROM monthly_data md
  JOIN latest_data_info ldi ON md.user_id = ldi.user_id
  WHERE md.period_start >= ldi.latest_date - INTERVAL '1 year'
),
timeframe_5y AS (
  SELECT 
    yd.user_id,
    '5Y' as timeframe,
    yd.latest_date_in_period as performance_date,
    yd.period_value as total_value,
    EXTRACT(YEAR FROM yd.period_start)::text as display_label
  FROM yearly_data yd
  JOIN latest_data_info ldi ON yd.user_id = ldi.user_id
  WHERE yd.period_start >= ldi.latest_date - INTERVAL '5 years'
)
-- Combine all timeframes
SELECT * FROM timeframe_1m
UNION ALL
SELECT * FROM timeframe_6m
UNION ALL
SELECT * FROM timeframe_1y
UNION ALL
SELECT * FROM timeframe_5y
ORDER BY timeframe, performance_date;

-- Grant access to the view
GRANT SELECT ON portfolio_timeframe_data TO authenticated;

-- Create the clean chart view with proper ordering
CREATE OR REPLACE VIEW portfolio_chart_clean AS
WITH deduplicated_data AS (
  SELECT DISTINCT ON (user_id, timeframe, display_label)
    user_id,
    timeframe,
    performance_date,
    total_value,
    display_label
  FROM portfolio_timeframe_data
  ORDER BY user_id, timeframe, display_label, performance_date DESC
)
SELECT 
  user_id,
  timeframe,
  performance_date,
  total_value,
  display_label,
  ROW_NUMBER() OVER (PARTITION BY user_id, timeframe ORDER BY performance_date) as sequence_number
FROM deduplicated_data
ORDER BY timeframe, performance_date;

-- Grant access to the clean chart view
GRANT SELECT ON portfolio_chart_clean TO authenticated;
