import React from 'react';
import { NavLink } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Upload, 
  Pie<PERSON><PERSON>, 
  Settings, 
  X 
} from 'lucide-react';

interface SidebarProps {
  closeSidebar: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ closeSidebar }) => {
  const navigation = [
    { name: 'Dashboard', href: '/', icon: LayoutDashboard },
    { name: 'Upload Assets', href: '/upload', icon: Upload },
    { name: 'Portfolio', href: '/portfolio', icon: Pie<PERSON><PERSON> },
    { name: 'Settings', href: '/settings', icon: Settings },
  ];

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="flex items-center justify-between h-16 flex-shrink-0 px-4 bg-white lg:hidden">
        <h1 className="text-xl font-bold text-black">AssetTracker</h1>
        <button
          type="button"
          className="h-10 w-10 rounded-md inline-flex items-center justify-center text-gray-600 hover:text-black focus:outline-none focus:ring-2 focus:ring-inset focus:ring-gray-500"
          onClick={closeSidebar}
        >
          <span className="sr-only">Close sidebar</span>
          <X className="h-6 w-6" />
        </button>
      </div>
      <div className="mt-5 flex-1 flex flex-col overflow-y-auto">
        <nav className="flex-1 px-2 space-y-1">
          {navigation.map((item) => (
            <NavLink
              key={item.name}
              to={item.href}
              className={({ isActive }) =>
                `${isActive ? 'bg-gray-100 text-black' : 'text-gray-600 hover:bg-gray-50 hover:text-black'}
                 group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-150`
              }
            >
              {({ isActive }) => (
                <>
                  <item.icon
                    className={`${
                      isActive ? 'text-black' : 'text-gray-400 group-hover:text-gray-600'
                    } mr-3 h-5 w-5 transition-colors duration-150`}
                  />
                  {item.name}
                </>
              )}
            </NavLink>
          ))}
        </nav>
      </div>
    </div>
  );
};