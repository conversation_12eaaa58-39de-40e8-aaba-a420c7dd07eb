import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { supabase } from '../utils/supabaseClient';

interface PerformanceData {
  name: string;
  value: number;
  date: string;
}

export const PriceChart: React.FC = () => {
  const [activeTimeframe, setActiveTimeframe] = useState<string>('1Y');
  const [data, setData] = useState<PerformanceData[]>([]);
  const [loading, setLoading] = useState(true);

  const timeframes = ['1M', '6M', '1Y', '5Y'];

  useEffect(() => {
    fetchPerformanceData();
  }, [activeTimeframe]);

  const fetchPerformanceData = async () => {
    setLoading(true);
    try {
      // Try to get data from the clean chart view first
      const { data: timeframeData, error: timeframeError } = await supabase
        .from('portfolio_chart_clean')
        .select('*')
        .eq('timeframe', activeTimeframe)
        .order('sequence_number', { ascending: true });

      if (timeframeError) {
        console.log('Timeframe view error:', timeframeError);
      }

      if (timeframeData && timeframeData.length > 0) {
        // Sort data by date to ensure proper chronological order
        const sortedData = timeframeData.sort((a: any, b: any) =>
          new Date(a.performance_date).getTime() - new Date(b.performance_date).getTime()
        );

        // Use the pre-formatted data from the view
        const chartData = sortedData.map((item: any) => ({
          name: item.display_label,
          value: Math.round(item.total_value || 0),
          date: item.performance_date
        }));

        setData(chartData);
      } else {
        // Try the original timeframe view as fallback
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('portfolio_timeframe_data')
          .select('*')
          .eq('timeframe', activeTimeframe)
          .order('performance_date', { ascending: true });

        if (fallbackError) {
          console.log('Fallback view error:', fallbackError);
        }

        if (fallbackData && fallbackData.length > 0) {
          // Sort fallback data by date as well
          const sortedFallbackData = fallbackData.sort((a: any, b: any) =>
            new Date(a.performance_date).getTime() - new Date(b.performance_date).getTime()
          );

          const chartData = sortedFallbackData.map((item: any) => ({
            name: item.display_label,
            value: Math.round(item.total_value || 0),
            date: item.performance_date
          }));

          setData(chartData);
        } else {
          // Fallback to portfolio_value_evolution with flexible filtering
          const { data: portfolioData, error: portfolioError } = await supabase
            .from('portfolio_value_evolution')
            .select('*')
            .order('value_date', { ascending: true });

          if (portfolioError) throw portfolioError;

          if (portfolioData && portfolioData.length > 0) {
            // For limited data, show all available data regardless of strict timeframe
            let dataToShow = portfolioData;

            // Only apply strict filtering if we have enough data points
            if (portfolioData.length > 5) {
              const now = new Date();
              let startDate = new Date();

              switch (activeTimeframe) {
                case '1M':
                  startDate.setMonth(now.getMonth() - 1);
                  break;
                case '6M':
                  startDate.setMonth(now.getMonth() - 6);
                  break;
                case '1Y':
                  startDate.setFullYear(now.getFullYear() - 1);
                  break;
                case '5Y':
                  startDate.setFullYear(now.getFullYear() - 5);
                  break;
              }

              const filteredData = portfolioData.filter(item =>
                new Date(item.value_date) >= startDate
              );

              // Use filtered data only if it has enough points, otherwise show all
              if (filteredData.length >= 2) {
                dataToShow = filteredData;
              }
            }

            if (dataToShow.length > 0) {
              // Sort the data by date before creating chart data
              const sortedDataToShow = dataToShow.sort((a: any, b: any) =>
                new Date(a.value_date).getTime() - new Date(b.value_date).getTime()
              );

              const chartData = sortedDataToShow.map((item: any) => ({
                name: formatDateLabel(item.value_date, activeTimeframe),
                value: Math.round(item.portfolio_value || 0),
                date: item.value_date
              }));

              setData(chartData);
            } else {
              // Fallback to sample data if no real data available
              setData(getFallbackData());
            }
          } else {
            // Fallback to sample data if no real data available
            setData(getFallbackData());
          }
        }
      }
    } catch (error) {
      console.error('Error fetching performance data:', error);
      // Fallback to sample data if no real data available
      setData(getFallbackData());
    } finally {
      setLoading(false);
    }
  };

  const formatDateLabel = (dateString: string, timeframe: string): string => {
    const date = new Date(dateString);

    switch (timeframe) {
      case '1M':
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      case '6M':
        return date.toLocaleDateString('en-US', { month: 'short' });
      case '1Y':
        return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
      case '5Y':
        return date.getFullYear().toString();
      default:
        return date.toLocaleDateString('en-US', { month: 'short' });
    }
  };

  const getFallbackData = (): PerformanceData[] => {
    // Fallback data when no real data is available - using current dates
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    const fallbackDataMap = {
      '1M': [
        { name: 'Week 1', value: 124000, date: new Date(currentYear, currentMonth, 1).toISOString() },
        { name: 'Week 2', value: 126500, date: new Date(currentYear, currentMonth, 8).toISOString() },
        { name: 'Week 3', value: 128800, date: new Date(currentYear, currentMonth, 15).toISOString() },
        { name: 'Week 4', value: 132200, date: new Date(currentYear, currentMonth, 22).toISOString() }
      ],
      '6M': [
        { name: 'Jan 24', value: 120000, date: new Date(currentYear, 0, 15).toISOString() },
        { name: 'Feb 24', value: 123000, date: new Date(currentYear, 1, 15).toISOString() },
        { name: 'Mar 24', value: 125500, date: new Date(currentYear, 2, 15).toISOString() },
        { name: 'Apr 24', value: 128000, date: new Date(currentYear, 3, 15).toISOString() },
        { name: 'May 24', value: 130000, date: new Date(currentYear, 4, 15).toISOString() },
        { name: 'Jun 24', value: 132200, date: new Date(currentYear, 5, 15).toISOString() }
      ],
      '1Y': [
        { name: 'Jan 24', value: 118000, date: new Date(currentYear, 0, 15).toISOString() },
        { name: 'Feb 24', value: 119000, date: new Date(currentYear, 1, 15).toISOString() },
        { name: 'Mar 24', value: 120500, date: new Date(currentYear, 2, 15).toISOString() },
        { name: 'Apr 24', value: 122780, date: new Date(currentYear, 3, 15).toISOString() },
        { name: 'May 24', value: 125890, date: new Date(currentYear, 4, 15).toISOString() },
        { name: 'Jun 24', value: 128390, date: new Date(currentYear, 5, 15).toISOString() },
        { name: 'Jul 24', value: 129490, date: new Date(currentYear, 6, 15).toISOString() },
        { name: 'Aug 24', value: 131000, date: new Date(currentYear, 7, 15).toISOString() },
        { name: 'Sep 24', value: 131500, date: new Date(currentYear, 8, 15).toISOString() },
        { name: 'Oct 24', value: 132200, date: new Date(currentYear, 9, 15).toISOString() },
        { name: 'Nov 24', value: 131800, date: new Date(currentYear, 10, 15).toISOString() },
        { name: 'Dec 24', value: 133000, date: new Date(currentYear, 11, 15).toISOString() }
      ],
      '5Y': [
        { name: '2020', value: 85000, date: new Date(2020, 6, 1).toISOString() },
        { name: '2021', value: 98000, date: new Date(2021, 6, 1).toISOString() },
        { name: '2022', value: 106500, date: new Date(2022, 6, 1).toISOString() },
        { name: '2023', value: 118000, date: new Date(2023, 6, 1).toISOString() },
        { name: '2024', value: 133000, date: new Date(2024, 6, 1).toISOString() }
      ]
    };

    return fallbackDataMap[activeTimeframe as keyof typeof fallbackDataMap] || fallbackDataMap['1Y'];
  };

  return (
    <div>
      <div className="mb-4 flex space-x-2 overflow-x-auto">
        {timeframes.map((timeframe) => (
          <button
            key={timeframe}
            className={`px-3 py-1 text-sm rounded-md ${
              activeTimeframe === timeframe
                ? 'bg-black text-white font-medium'
                : 'text-gray-500 hover:bg-gray-100 border border-gray-300'
            } transition-colors duration-150`}
            onClick={() => setActiveTimeframe(timeframe)}
          >
            {timeframe}
          </button>
        ))}
      </div>

      <div className="h-80">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
              <XAxis dataKey="name" stroke="#666" />
              <YAxis
                stroke="#666"
                tickFormatter={(value) => `$${value.toLocaleString()}`}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  color: 'black'
                }}
                formatter={(value: number) => [`$${value.toLocaleString()}`, 'Portfolio Value']}
                labelFormatter={(label) => `Period: ${label}`}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#000000"
                activeDot={{ r: 8, fill: '#000000' }}
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        )}
      </div>
    </div>
  );
};