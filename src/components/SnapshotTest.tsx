import React, { useState, useEffect } from 'react';
import { supabase } from '../utils/supabaseClient';

interface Snapshot {
  id: string;
  asset_identifier: string;
  name: string;
  quantity: number;
  unit_price: number | null;
  snapshot_type: string;
  created_at: string;
}

export const SnapshotTest: React.FC = () => {
  const [snapshots, setSnapshots] = useState<Snapshot[]>([]);
  const [latestAssets, setLatestAssets] = useState<any[]>([]);
  const [selectedAssetId, setSelectedAssetId] = useState<string>('');

  const fetchSnapshots = async () => {
    try {
      const { data, error } = await supabase
        .from('assets')
        .select('id, asset_identifier, name, quantity, unit_price, snapshot_type, created_at')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setSnapshots(data || []);
    } catch (error) {
      console.error('Error fetching snapshots:', error);
    }
  };

  const fetchLatestAssets = async () => {
    try {
      const { data, error } = await supabase
        .from('latest_assets')
        .select('*')
        .neq('snapshot_type', 'delete');

      if (error) throw error;
      setLatestAssets(data || []);
    } catch (error) {
      console.error('Error fetching latest assets:', error);
    }
  };

  useEffect(() => {
    fetchSnapshots();
    fetchLatestAssets();
  }, []);

  const getSnapshotsForAsset = (assetIdentifier: string) => {
    return snapshots.filter(s => s.asset_identifier === assetIdentifier);
  };

  const uniqueAssetIdentifiers = Array.from(new Set(snapshots.map(s => s.asset_identifier)));

  return (
    <div className="p-6 bg-white">
      <h2 className="text-xl font-bold mb-4">Snapshot Pattern Test</h2>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Latest Assets View */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Latest Assets (Current State)</h3>
          <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
            {latestAssets.length === 0 ? (
              <p className="text-gray-500">No assets found</p>
            ) : (
              <div className="space-y-2">
                {latestAssets.map((asset) => (
                  <div key={asset.id} className="bg-white p-3 rounded border">
                    <div className="font-medium">{asset.name}</div>
                    <div className="text-sm text-gray-600">
                      Qty: {asset.quantity} | Price: {asset.unit_price ? `$${asset.unit_price}` : 'NULL'}
                    </div>
                    <div className="text-xs text-gray-500">
                      Type: {asset.snapshot_type} | ID: {asset.asset_identifier.slice(0, 8)}...
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* All Snapshots */}
        <div>
          <h3 className="text-lg font-semibold mb-3">All Snapshots (History)</h3>
          <div className="mb-3">
            <select
              value={selectedAssetId}
              onChange={(e) => setSelectedAssetId(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">All Assets</option>
              {uniqueAssetIdentifiers.map(id => {
                const firstSnapshot = snapshots.find(s => s.asset_identifier === id);
                return (
                  <option key={id} value={id}>
                    {firstSnapshot?.name} ({id.slice(0, 8)}...)
                  </option>
                );
              })}
            </select>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
            {snapshots.length === 0 ? (
              <p className="text-gray-500">No snapshots found</p>
            ) : (
              <div className="space-y-2">
                {(selectedAssetId ? getSnapshotsForAsset(selectedAssetId) : snapshots).map((snapshot) => (
                  <div key={snapshot.id} className={`p-3 rounded border ${
                    snapshot.snapshot_type === 'create' ? 'bg-green-50 border-green-200' :
                    snapshot.snapshot_type === 'update' ? 'bg-blue-50 border-blue-200' :
                    'bg-red-50 border-red-200'
                  }`}>
                    <div className="font-medium">{snapshot.name}</div>
                    <div className="text-sm">
                      Qty: {snapshot.quantity} | Price: {snapshot.unit_price ? `$${snapshot.unit_price}` : 'NULL'}
                    </div>
                    <div className="text-xs text-gray-600">
                      Type: <span className="font-medium">{snapshot.snapshot_type}</span> | 
                      Time: {new Date(snapshot.created_at).toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      Asset ID: {snapshot.asset_identifier.slice(0, 8)}... | 
                      Snapshot ID: {snapshot.id.slice(0, 8)}...
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="mt-6 flex space-x-4">
        <button
          onClick={fetchSnapshots}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Refresh Snapshots
        </button>
        <button
          onClick={fetchLatestAssets}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
        >
          Refresh Latest Assets
        </button>
      </div>

      <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
        <h4 className="font-semibold mb-2">How to Test:</h4>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Go to Upload Assets page and create some assets</li>
          <li>Save the assets - this creates 'create' snapshots</li>
          <li>Modify quantities/prices and save - this creates 'update' snapshots</li>
          <li>Mark assets for deletion and save - this creates 'delete' snapshots</li>
          <li>Come back here to see the snapshot history</li>
        </ol>
      </div>
    </div>
  );
};
