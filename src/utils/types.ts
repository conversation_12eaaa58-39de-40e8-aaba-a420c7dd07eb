export interface Asset {
  id: string;
  name: string;
  symbol: string;
  type: string;
  price: number;
  change: number;
  positive: boolean;
  quantity: number;
  value: number;
  purchasePrice: number;
  purchaseDate: string;
  profit: number;
  profitPercentage: number;
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  country: string;
  currency: string;
}

export interface ApiConfig {
  provider: string;
  apiKey: string;
  updateFrequency: string;
}

export interface NotificationSettings {
  priceAlerts: boolean;
  marketNews: boolean;
  portfolioReports: boolean;
}