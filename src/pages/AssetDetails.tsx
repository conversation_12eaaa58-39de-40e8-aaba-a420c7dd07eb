import React from 'react';
import { useParams } from 'react-router-dom';
import { TrendingUp, TrendingDown, DollarSign, Calendar, Briefcase, Edit, Trash2 } from 'lucide-react';
import { PriceChart } from '../components/PriceChart';

export const AssetDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  
  // This would be replaced with real data from your API/state
  const asset = {
    id: '1',
    name: 'Apple Inc.',
    symbol: 'AAPL',
    type: 'Stock',
    description: 'Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories worldwide. The company offers iPhone, a line of smartphones.',
    price: 145.86,
    change: 2.3,
    positive: true,
    quantity: 10,
    value: 1458.60,
    purchasePrice: 132.45,
    purchaseDate: '2023-01-15',
    profit: 134.10,
    profitPercentage: 9.37,
    marketCap: '2.43T',
    volume: '78.42M',
    peRatio: '28.5',
    dividend: '0.88',
    sector: 'Technology',
    industry: 'Consumer Electronics',
  };

  const timeframes = ['1D', '1W', '1M', '3M', '6M', 'YTD', '1Y', '5Y'];
  const [activeTimeframe, setActiveTimeframe] = React.useState('1M');

  return (
    <div>
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{asset.name}</h1>
            <p className="text-sm text-gray-500 mt-1">{asset.symbol} • {asset.type}</p>
          </div>
          <div className="flex space-x-2">
            <button className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </button>
            <button className="inline-flex items-center px-3 py-1.5 border border-red-300 shadow-sm text-sm font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </button>
          </div>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          <div className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-3">
            <div className="sm:col-span-1">
              <div className="text-sm font-medium text-gray-500">Current Price</div>
              <div className="mt-1 text-xl font-semibold text-gray-900">
                ${asset.price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </div>
              <div className={`mt-1 text-sm inline-flex items-center ${
                asset.positive ? 'text-green-600' : 'text-red-600'
              }`}>
                {asset.positive ? (
                  <TrendingUp className="mr-1 h-4 w-4" />
                ) : (
                  <TrendingDown className="mr-1 h-4 w-4" />
                )}
                {asset.positive ? '+' : ''}{asset.change}%
              </div>
            </div>

            <div className="sm:col-span-1">
              <div className="text-sm font-medium text-gray-500">Your Position</div>
              <div className="mt-1 text-xl font-semibold text-gray-900">
                ${asset.value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </div>
              <div className="mt-1 text-sm text-gray-500">
                {asset.quantity} {asset.quantity > 1 ? 'units' : 'unit'} @ ${asset.purchasePrice}
              </div>
            </div>

            <div className="sm:col-span-1">
              <div className="text-sm font-medium text-gray-500">Profit/Loss</div>
              <div className={`mt-1 text-xl font-semibold ${
                asset.profit >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                ${Math.abs(asset.profit).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </div>
              <div className={`mt-1 text-sm ${
                asset.profitPercentage >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {asset.profitPercentage >= 0 ? '+' : ''}{asset.profitPercentage.toFixed(2)}%
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Price History</h3>
          <div className="mt-4 flex space-x-2 overflow-x-auto">
            {timeframes.map((timeframe) => (
              <button
                key={timeframe}
                className={`px-3 py-1 text-sm rounded-md ${
                  activeTimeframe === timeframe
                    ? 'bg-blue-100 text-blue-700 font-medium'
                    : 'text-gray-500 hover:bg-gray-100'
                } transition-colors duration-150`}
                onClick={() => setActiveTimeframe(timeframe)}
              >
                {timeframe}
              </button>
            ))}
          </div>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
          <div className="h-80 p-6">
            <PriceChart />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Asset Information</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">Key details and market data</p>
          </div>
          <div className="border-t border-gray-200">
            <dl>
              <div className="bg-gray-50 px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500 flex items-center">
                  <DollarSign className="mr-1 h-4 w-4 text-gray-400" />
                  Market Cap
                </dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{asset.marketCap}</dd>
              </div>
              <div className="bg-white px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Volume</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{asset.volume}</dd>
              </div>
              <div className="bg-gray-50 px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">P/E Ratio</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{asset.peRatio}</dd>
              </div>
              <div className="bg-white px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Dividend Yield</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{asset.dividend}</dd>
              </div>
              <div className="bg-gray-50 px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500 flex items-center">
                  <Briefcase className="mr-1 h-4 w-4 text-gray-400" />
                  Sector
                </dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{asset.sector}</dd>
              </div>
              <div className="bg-white px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Industry</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{asset.industry}</dd>
              </div>
            </dl>
          </div>
        </div>

        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Purchase Information</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">Details about your investment</p>
          </div>
          <div className="border-t border-gray-200">
            <dl>
              <div className="bg-gray-50 px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500 flex items-center">
                  <Calendar className="mr-1 h-4 w-4 text-gray-400" />
                  Purchase Date
                </dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{asset.purchaseDate}</dd>
              </div>
              <div className="bg-white px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Purchase Price</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  ${asset.purchasePrice.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </dd>
              </div>
              <div className="bg-gray-50 px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Quantity</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{asset.quantity}</dd>
              </div>
              <div className="bg-white px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Total Cost</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  ${(asset.purchasePrice * asset.quantity).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </dd>
              </div>
              <div className="bg-gray-50 px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Current Value</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  ${asset.value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </dd>
              </div>
              <div className="bg-white px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Profit/Loss</dt>
                <dd className={`mt-1 text-sm sm:mt-0 sm:col-span-2 ${
                  asset.profit >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  ${Math.abs(asset.profit).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  <span className="ml-2">
                    ({asset.profitPercentage >= 0 ? '+' : ''}{asset.profitPercentage.toFixed(2)}%)
                  </span>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Asset Description</h3>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          <p className="text-sm text-gray-500">{asset.description}</p>
        </div>
      </div>
    </div>
  );
};