import React from 'react';
import { Link } from 'react-router-dom';
import { TrendingUp, TrendingDown, Filter, Download } from 'lucide-react';

export const Portfolio: React.FC = () => {
  // Sample data
  const assets = [
    { 
      id: '1', 
      name: 'Apple Inc.', 
      symbol: 'AAPL', 
      type: 'Stock', 
      price: 145.86, 
      change: 2.3, 
      positive: true,
      quantity: 10,
      value: 1458.60,
      purchasePrice: 132.45,
      purchaseDate: '2023-01-15',
      profit: 134.10,
      profitPercentage: 9.37,
    },
    { 
      id: '2', 
      name: 'Bitcoin', 
      symbol: 'BTC', 
      type: 'Cryptocurrency', 
      price: 38452.12, 
      change: 1.8, 
      positive: true,
      quantity: 0.5,
      value: 19226.06,
      purchasePrice: 35000.00,
      purchaseDate: '2023-02-10',
      profit: 1726.06,
      profitPercentage: 9.86,
    },
    { 
      id: '3', 
      name: 'Tesla Inc.', 
      symbol: 'TSLA', 
      type: 'Stock', 
      price: 654.87, 
      change: -0.7, 
      positive: false,
      quantity: 5,
      value: 3274.35,
      purchasePrice: 700.00,
      purchaseDate: '2023-01-20',
      profit: -225.65,
      profitPercentage: -6.45,
    },
    { 
      id: '4', 
      name: 'Ethereum', 
      symbol: 'ETH', 
      type: 'Cryptocurrency', 
      price: 2345.67, 
      change: -2.1, 
      positive: false,
      quantity: 2,
      value: 4691.34,
      purchasePrice: 2500.00,
      purchaseDate: '2023-03-05',
      profit: -308.66,
      profitPercentage: -6.18,
    },
    { 
      id: '5', 
      name: 'Miami Property', 
      symbol: 'N/A', 
      type: 'Real Estate', 
      price: 750000, 
      change: 3.5, 
      positive: true,
      quantity: 1,
      value: 750000,
      purchasePrice: 725000,
      purchaseDate: '2022-11-12',
      profit: 25000,
      profitPercentage: 3.45,
    },
  ];

  const totalValue = assets.reduce((acc, asset) => acc + asset.value, 0);
  const totalProfit = assets.reduce((acc, asset) => acc + asset.profit, 0);
  const totalProfitPercentage = (totalProfit / (totalValue - totalProfit)) * 100;

  return (
    <div>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Portfolio</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage and track all your assets in one place
          </p>
        </div>
        <div className="flex space-x-3 mt-4 md:mt-0">
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <Filter className="-ml-1 mr-2 h-5 w-5 text-gray-500" />
            Filter
          </button>
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <Download className="-ml-1 mr-2 h-5 w-5 text-gray-500" />
            Export
          </button>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Portfolio Summary</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Totals and performance metrics</p>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Total Value</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">${totalValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Total Profit/Loss</dt>
              <dd className={`mt-1 text-sm sm:mt-0 sm:col-span-2 ${totalProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                <span className="inline-flex items-center">
                  {totalProfit >= 0 ? (
                    <TrendingUp className="mr-1 h-4 w-4" />
                  ) : (
                    <TrendingDown className="mr-1 h-4 w-4" />
                  )}
                  ${Math.abs(totalProfit).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  <span className="ml-2">
                    ({totalProfitPercentage >= 0 ? '+' : ''}{totalProfitPercentage.toFixed(2)}%)
                  </span>
                </span>
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Number of Assets</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{assets.length}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">June 25, 2023</dd>
            </div>
          </dl>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="flex flex-col">
          <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Asset
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Type
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Current Price
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Value
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Profit/Loss
                    </th>
                    <th scope="col" className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {assets.map((asset) => (
                    <tr key={asset.id} className="hover:bg-gray-50 transition-colors duration-150">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              <Link to={`/asset/${asset.id}`} className="hover:text-blue-600">
                                {asset.name}
                              </Link>
                            </div>
                            <div className="text-sm text-gray-500">{asset.symbol}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{asset.type}</div>
                        <div className="text-sm text-gray-500">Qty: {asset.quantity}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          ${asset.price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </div>
                        <div className={`text-xs flex items-center ${
                          asset.positive ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {asset.positive ? (
                            <TrendingUp className="mr-1 h-3 w-3" />
                          ) : (
                            <TrendingDown className="mr-1 h-3 w-3" />
                          )}
                          {asset.positive ? '+' : ''}{asset.change}%
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          ${asset.value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </div>
                        <div className="text-xs text-gray-500">
                          Purchase: ${asset.purchasePrice.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm ${
                          asset.profit >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          ${Math.abs(asset.profit).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                          <span className="ml-1">
                            ({asset.profitPercentage >= 0 ? '+' : ''}{asset.profitPercentage.toFixed(2)}%)
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <Link
                          to={`/asset/${asset.id}`}
                          className="text-blue-600 hover:text-blue-900 mr-4"
                        >
                          View
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};