import React from 'react';
import { Link } from 'react-router-dom';
import { Home } from 'lucide-react';

export const NotFound: React.FC = () => {
  return (
    <div className="min-h-full pt-16 pb-12 flex flex-col bg-white">
      <main className="flex-grow flex flex-col justify-center max-w-7xl w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex-shrink-0 flex justify-center">
          <a href="/" className="inline-flex">
            <span className="sr-only">Asset Portfolio</span>
            <div className="h-12 w-12 rounded-full bg-blue-600 flex items-center justify-center text-white text-2xl font-bold">A</div>
          </a>
        </div>
        <div className="py-16">
          <div className="text-center">
            <p className="text-sm font-semibold text-blue-600 uppercase tracking-wide">404 error</p>
            <h1 className="mt-2 text-4xl font-extrabold text-gray-900 tracking-tight sm:text-5xl">Page not found.</h1>
            <p className="mt-2 text-base text-gray-500">Sorry, we couldn't find the page you're looking for.</p>
            <div className="mt-6">
              <Link to="/" className="text-base font-medium text-blue-600 hover:text-blue-500">
                <div className="flex items-center justify-center">
                  <Home className="mr-1 h-5 w-5" />
                  Go back home
                </div>
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};