-- Quick verification queries for the seed data

-- Check if buckets were created
SELECT 'Buckets' as table_name, COUNT(*) as count FROM buckets;

-- Check if assets were created
SELECT 'Assets' as table_name, COUNT(*) as count FROM assets;

-- Check latest assets view
SELECT 'Latest Assets' as table_name, COUNT(*) as count FROM latest_assets;

-- Sample of the data
SELECT 
  name,
  symbol,
  type,
  quantity,
  unit_price,
  holding_date,
  country
FROM latest_assets
ORDER BY name
LIMIT 10;
