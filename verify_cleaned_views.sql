-- Verify the cleaned up database views

-- Check remaining views
SELECT 
  'Remaining Views' as check_type,
  viewname,
  obj_description(c.oid, 'pg_class') as comment
FROM pg_views pv
JOIN pg_class c ON c.relname = pv.viewname
WHERE pv.schemaname = 'public'
ORDER BY pv.viewname;

-- Test each remaining view to ensure they work
SELECT 'latest_assets' as view_name, COUNT(*) as record_count FROM latest_assets;
SELECT 'portfolio_value_evolution' as view_name, COUNT(*) as record_count FROM portfolio_value_evolution;
SELECT 'portfolio_timeframe_data' as view_name, COUNT(*) as record_count FROM portfolio_timeframe_data;
SELECT 'portfolio_chart_clean' as view_name, COUNT(*) as record_count FROM portfolio_chart_clean;

-- Verify chart data is still working for all timeframes
SELECT 
  'Chart Data by Timeframe' as check_type,
  timeframe,
  COUNT(*) as record_count
FROM portfolio_chart_clean
GROUP BY timeframe
ORDER BY timeframe;
